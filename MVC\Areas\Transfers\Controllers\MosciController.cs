using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc;
using Odrdc.Dots.Areas.Transfers.Models.Mosci;

namespace Odrdc.Dots.Areas.Transfers.Controllers
{
    [Area("Transfers")]
    public class MosciController : Controller
    {
        // Simulated in-memory storage for demonstration
        private static List<MosciViewModel> _mosciList = new List<MosciViewModel>();

        static MosciController()
        {
            if (_mosciList.Count == 0)
            {
                // Add sample data with different offender IDs
                _mosciList.Add(new MosciViewModel
                {
                    Recno = 1,
                    SchDate = DateTime.Now,
                    SchdInst = "MOSCI",
                    Instno = "12345",
                    Oid = "OID001",
                    Descrl = "Sample Description 1",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R001",
                    LastName = "SMITH",
                    FirstName = "JOHN",
                    InmateIdPrefix = "A",
                    OffenderId = "123456"
                });

                _mosciList.Add(new MosciViewModel
                {
                    Recno = 2,
                    SchDate = DateTime.Now.AddDays(1),
                    SchdInst = "MOSCI",
                    Instno = "12346",
                    Oid = "OID002",
                    Descrl = "Sample Description 2",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R002",
                    LastName = "JOHNSON",
                    FirstName = "ROBERT",
                    InmateIdPrefix = "A",
                    OffenderId = "123456"
                });

                _mosciList.Add(new MosciViewModel
                {
                    Recno = 3,
                    SchDate = DateTime.Now.AddDays(2),
                    SchdInst = "MOSCI",
                    Instno = "12347",
                    Oid = "OID003",
                    Descrl = "Sample Description 3",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R003",
                    LastName = "WILLIAMS",
                    FirstName = "MICHAEL",
                    InmateIdPrefix = "R",
                    OffenderId = "345678"
                });

                // Add two inmates with the same offender ID but different prefixes
                _mosciList.Add(new MosciViewModel
                {
                    Recno = 4,
                    SchDate = DateTime.Now.AddDays(3),
                    SchdInst = "MOSCI",
                    Instno = "12348",
                    Oid = "OID004",
                    Descrl = "Sample Description 4",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R004",
                    LastName = "BROWN",
                    FirstName = "DAVID",
                    InmateIdPrefix = "A",
                    OffenderId = "456789"
                });

                _mosciList.Add(new MosciViewModel
                {
                    Recno = 5,
                    SchDate = DateTime.Now.AddDays(4),
                    SchdInst = "MOSCI",
                    Instno = "12349",
                    Oid = "OID005",
                    Descrl = "Sample Description 5",
                    Sts = "Active",
                    Stationame = "MANCINI",
                    SysDate = DateTime.Now,
                    Romid = "R005",
                    LastName = "BROWN",
                    FirstName = "DAVID",
                    InmateIdPrefix = "W",
                    OffenderId = "456789"
                });
            }
        }

        // GET: Transfers/Mosci
        public IActionResult Index(string searchPrefix = "", string searchOffenderId = "", string action = "")
        {
            var model = new MosciPageViewModel();

            // Handle search action
            if (action == "Search" && !string.IsNullOrWhiteSpace(searchOffenderId))
            {
                model.SearchPrefix = searchPrefix ?? "A";
                model.SearchOffenderId = searchOffenderId;

                // Perform search - combine prefix and offender ID for search
                var combinedSearchId = model.SearchPrefix + searchOffenderId;
                var searchResults = PerformInmateSearchFromCombined(combinedSearchId);

                // Clear the inmates list and add search results
                model.Inmates.Clear();

                if (searchResults.Any())
                {
                    // Add all search results
                    foreach (var result in searchResults)
                    {
                        model.Inmates.Add(new MosciViewModel
                        {
                            Recno = result.Recno,
                            SchDate = result.SchDate,
                            SchdInst = result.SchdInst,
                            Instno = result.Instno,
                            Oid = result.Oid,
                            Descrl = result.Descrl,
                            Sts = result.Sts,
                            Stationame = result.Stationame,
                            SysDate = result.SysDate,
                            Romid = result.Romid,
                            LastName = result.LastName,
                            FirstName = result.FirstName,
                            InmateIdPrefix = result.InmateIdPrefix,
                            OffenderId = result.OffenderId
                        });
                    }

                    // Add one empty row for new entries
                    if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                    {
                        model.Inmates.Add(CreateEmptyInmate());
                    }

                    model.HasSearchResults = true;
                    model.Message = $"Found {searchResults.Count} inmate(s) matching the search criteria.";
                }
                else
                {
                    // No results found - add one empty row
                    model.Inmates.Add(CreateEmptyInmate());
                    model.HasSearchResults = false;
                    model.ErrorMessage = "No inmates found matching the search criteria.";
                }
            }
            else if (action == "AddNew")
            {
                // Add a new empty row
                if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                {
                    model.Inmates.Add(CreateEmptyInmate());
                }
                else
                {
                    model.ErrorMessage = $"Maximum number of rows ({MosciPageViewModel.MaxRows}) reached. Cannot add more rows.";
                }
            }
            else
            {
                // Default view - show one empty row
                model.Inmates.Add(CreateEmptyInmate());
            }

            return View(model);
        }

        // Helper method to create an empty inmate
        private MosciViewModel CreateEmptyInmate()
        {
            return new MosciViewModel
            {
                SchDate = DateTime.Now,
                SysDate = DateTime.Now,
                InmateIdPrefix = "", // Don't set default prefix to avoid showing "A" in empty rows
                SchdInst = "",
                Instno = "",
                Oid = "",
                Descrl = "",
                Sts = "",
                Stationame = "",
                Romid = "",
                LastName = "",
                FirstName = "",
                OffenderId = ""
            };
        }

        // Helper method to perform inmate search
        private List<MosciViewModel> PerformInmateSearch(string? prefix, string offenderId)
        {
            var matchingInmates = new List<MosciViewModel>();

            if (!string.IsNullOrWhiteSpace(offenderId))
            {
                // Search logic
                if (!string.IsNullOrEmpty(prefix))
                {
                    matchingInmates = _mosciList.Where(i =>
                        i.InmateIdPrefix == prefix &&
                        i.OffenderId == offenderId).ToList();
                }
                else
                {
                    matchingInmates = _mosciList.Where(i =>
                        i.OffenderId == offenderId).ToList();
                }
            }

            return matchingInmates;
        }

        // Helper method to perform inmate search from combined ID (prefix + offender ID)
        private List<MosciViewModel> PerformInmateSearchFromCombined(string combinedId)
        {
            var matchingInmates = new List<MosciViewModel>();

            if (!string.IsNullOrWhiteSpace(combinedId))
            {
                // Extract the prefix (first character) and the numeric part
                string prefix = "";
                string offenderId = combinedId.Trim();

                // If the search ID starts with a letter (prefix), extract it
                if (combinedId.Length > 0 && char.IsLetter(combinedId[0]))
                {
                    prefix = combinedId[0].ToString().ToUpper();
                    offenderId = combinedId[1..];
                }

                // Console.WriteLine($"Server-side search - Extracted prefix: '{prefix}', offenderId: '{offenderId}'");

                if (!string.IsNullOrEmpty(prefix))
                {
                    // Search by both prefix and ID
                    matchingInmates = _mosciList.Where(i =>
                        i.InmateIdPrefix.Equals(prefix, StringComparison.OrdinalIgnoreCase) &&
                        i.OffenderId.Equals(offenderId, StringComparison.OrdinalIgnoreCase)).ToList();
                }
                else
                {
                    // Search by ID only (across all prefixes)
                    matchingInmates = _mosciList.Where(i =>
                        i.OffenderId.Equals(offenderId, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Console.WriteLine($"Server-side search found {matchingInmates.Count} matching inmates");
            }

            return matchingInmates;
        }

        // POST: Handle form submissions for various actions
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Index(MosciPageViewModel model, string submitAction = "")
        {
            try
            {
                // For search operations, we need to clear the model state to prevent
                // the existing table data from interfering with search results
                if (submitAction == "Search")
                {
                    // Clear all model state related to Inmates to prevent binding issues
                    var keysToRemove = ModelState.Keys.Where(k => k.StartsWith("Inmates")).ToList();
                    foreach (var key in keysToRemove)
                    {
                        ModelState.Remove(key);
                    }

                    // Ensure we have a fresh model for search
                    model.Inmates = new List<MosciViewModel>();
                }

                switch (submitAction)
                {
                    case "Search":
                        return HandleSearch(model);
                    case "AddNew":
                        return HandleAddNew(model);
                    case "RemoveSelected":
                        return HandleRemoveSelected(model);
                    case "DeleteSelected":
                        return HandleDeleteSelected(model);
                    case "Save":
                        return HandleSave(model);
                    case "Cancel":
                        return RedirectToAction("Index");
                    default:
                        return View(model);
                }
            }
            catch (Exception ex)
            {
                model.ErrorMessage = "An error occurred: " + ex.Message;
                return View(model);
            }
        }

        private IActionResult HandleSearch(MosciPageViewModel model)
        {
            if (string.IsNullOrWhiteSpace(model.SearchOffenderId))
            {
                model.ErrorMessage = "Please enter an Offender ID.";
                // Create a fresh list with one empty row
                model.Inmates = new List<MosciViewModel> { CreateEmptyInmate() };
                model.HasSearchResults = false;
                return View(model);
            }

            // Combine prefix and offender ID for search
            var combinedSearchId = model.SearchPrefix + model.SearchOffenderId;

            var searchResults = PerformInmateSearchFromCombined(combinedSearchId);

            // Create a completely new list to ensure clean state
            model.Inmates = new List<MosciViewModel>();

            if (searchResults.Any())
            {
                // Add all search results to the inmates list
                foreach (var result in searchResults)
                {
                    // Create a new instance to avoid reference issues
                    var inmateToAdd = new MosciViewModel
                    {
                        Recno = result.Recno,
                        SchDate = result.SchDate,
                        SchdInst = result.SchdInst,
                        Instno = result.Instno,
                        Oid = result.Oid,
                        Descrl = result.Descrl,
                        Sts = result.Sts,
                        Stationame = result.Stationame,
                        SysDate = result.SysDate,
                        Romid = result.Romid,
                        LastName = result.LastName,
                        FirstName = result.FirstName,
                        InmateIdPrefix = result.InmateIdPrefix,
                        OffenderId = result.OffenderId
                    };

                    model.Inmates.Add(inmateToAdd);
                }

                // Always ensure we have at least one empty row for adding new entries
                // But don't exceed the maximum number of rows
                if (model.Inmates.Count < MosciPageViewModel.MaxRows)
                {
                    model.Inmates.Add(CreateEmptyInmate());
                }

                model.HasSearchResults = true;
                model.Message = $"Found {searchResults.Count} inmate(s) matching the search criteria.";
            }
            else
            {
                // No search results found - show one empty row
                model.Inmates.Add(CreateEmptyInmate());
                model.HasSearchResults = false;
                model.ErrorMessage = "No inmates found matching the search criteria.";
            }

            // Clear ViewData to ensure clean rendering for search results
            ViewData.Clear();

            return View(model);
        }

        private IActionResult HandleAddNew(MosciPageViewModel model)
        {
            if (model.Inmates.Count >= MosciPageViewModel.MaxRows)
            {
                model.ErrorMessage = $"Maximum number of rows ({MosciPageViewModel.MaxRows}) reached. Cannot add more rows.";
            }
            else
            {
                model.Inmates.Add(CreateEmptyInmate());
                model.Message = "New row added successfully.";
            }

            return View(model);
        }

        private IActionResult HandleRemoveSelected(MosciPageViewModel model)
        {
            var itemsToRemove = model.Inmates.Where(i => i.IsMarkedForRemoval).ToList();

            if (!itemsToRemove.Any())
            {
                model.ErrorMessage = "Please select at least one inmate to remove.";
                return View(model);
            }

            // Ensure at least one row remains
            if (model.Inmates.Count - itemsToRemove.Count < 1)
            {
                model.ErrorMessage = "Cannot remove all rows. At least one row must remain in the table.";
                return View(model);
            }

            foreach (var item in itemsToRemove)
            {
                model.Inmates.Remove(item);
            }

            model.Message = $"Removed {itemsToRemove.Count} inmate(s) successfully.";
            return View(model);
        }

        private IActionResult HandleDeleteSelected(MosciPageViewModel model)
        {
            var itemsToDelete = model.Inmates.Where(i => i.IsMarkedForDeletion).ToList();

            if (!itemsToDelete.Any())
            {
                model.ErrorMessage = "Please select at least one inmate to delete.";
                return View(model);
            }

            // Ensure at least one row remains
            if (model.Inmates.Count - itemsToDelete.Count < 1)
            {
                model.ErrorMessage = "Cannot delete all rows. At least one row must remain in the table.";
                return View(model);
            }

            foreach (var item in itemsToDelete)
            {
                model.Inmates.Remove(item);
            }

            model.Message = $"Deleted {itemsToDelete.Count} inmate(s) successfully.";
            return View(model);
        }

        private IActionResult HandleSave(MosciPageViewModel model)
        {
            // Validate the model
            if (!ModelState.IsValid)
            {
                model.ErrorMessage = "Please correct the validation errors and try again.";
                return View(model);
            }

            // Save logic - update the static list with the current inmates
            // In a real application, this would save to a database
            foreach (var inmate in model.Inmates)
            {
                if (inmate.Recno == 0) // New inmate
                {
                    inmate.Recno = _mosciList.Count > 0 ? _mosciList.Max(m => m.Recno) + 1 : 1;
                    _mosciList.Add(inmate);
                }
                else // Existing inmate
                {
                    var existing = _mosciList.FirstOrDefault(m => m.Recno == inmate.Recno);
                    if (existing != null)
                    {
                        // Update existing inmate
                        existing.SchDate = inmate.SchDate;
                        existing.SchdInst = inmate.SchdInst;
                        existing.Instno = inmate.Instno;
                        existing.Oid = inmate.Oid;
                        existing.Descrl = inmate.Descrl;
                        existing.Sts = inmate.Sts;
                        existing.Stationame = inmate.Stationame;
                        existing.SysDate = inmate.SysDate;
                        existing.Romid = inmate.Romid;
                        existing.LastName = inmate.LastName;
                        existing.FirstName = inmate.FirstName;
                        existing.InmateIdPrefix = inmate.InmateIdPrefix;
                        existing.OffenderId = inmate.OffenderId;
                    }
                }
            }

            model.Message = "Inmate schedule saved successfully.";
            return View(model);
        }

        // GET: Transfers/Mosci/Create
        public IActionResult Create()
        {
            var model = new MosciViewModel();
            return View(model);
        }

        // POST: Transfers/Mosci/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Create(MosciViewModel model)
        {
            if (ModelState.IsValid)
            {
                // Simulate auto-increment Recno
                model.Recno = _mosciList.Count > 0 ? _mosciList.Max(m => m.Recno) + 1 : 1;
                _mosciList.Add(model);
                TempData["Message"] = "Record created successfully.";
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }

        // GET: Transfers/Mosci/Edit/5
        public IActionResult Edit(int id)
        {
            var model = _mosciList.FirstOrDefault(m => m.Recno == id);
            if (model == null)
            {
                return NotFound();
            }
            return View(model);
        }

        // POST: Transfers/Mosci/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Edit(int id, MosciViewModel model)
        {
            if (ModelState.IsValid)
            {
                var existing = _mosciList.FirstOrDefault(m => m.Recno == id);
                if (existing == null)
                {
                    return NotFound();
                }
                // Update properties
                existing.SchDate = model.SchDate;
                existing.SchdInst = model.SchdInst;
                existing.Instno = model.Instno;
                existing.Oid = model.Oid;
                existing.Descrl = model.Descrl;
                existing.Sts = model.Sts;
                existing.Stationame = model.Stationame;
                existing.SysDate = model.SysDate;
                existing.Romid = model.Romid;
                existing.LastName = model.LastName;
                existing.FirstName = model.FirstName;
                existing.InmateIdPrefix = model.InmateIdPrefix;
                existing.OffenderId = model.OffenderId;
                TempData["Message"] = "Record updated successfully.";
                return RedirectToAction(nameof(Index));
            }
            return View(model);
        }

        // GET: Transfers/Mosci/Delete/5
        public IActionResult Delete(int id)
        {
            var model = _mosciList.FirstOrDefault(m => m.Recno == id);
            if (model == null)
            {
                return NotFound();
            }
            return View(model);
        }

        // POST: Transfers/Mosci/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public IActionResult DeleteConfirmed(int id)
        {
            var model = _mosciList.FirstOrDefault(m => m.Recno == id);
            if (model != null)
            {
                _mosciList.Remove(model);
                TempData["Message"] = "Record deleted successfully.";
            }
            return RedirectToAction(nameof(Index));
        }

        // POST: Transfers/Mosci/SaveNexus
        [HttpPost]
        public IActionResult SaveNexus([FromBody] List<MosciViewModel> models)
        {
            try
            {
                if (models != null && models.Count > 0)
                {
                    // Save logic: replace the list with the submitted data
                    _mosciList = models;

                    // Return success message
                    return Json(new { success = true, message = "Inmate schedule saved successfully." });
                }
                else
                {
                    // Return error message
                    return Json(new { success = false, message = "No data received." });
                }
            }
            catch (Exception ex)
            {
                // Return error message
                return Json(new { success = false, message = "Error saving inmate schedule: " + ex.Message });
            }
        }

        // GET: Transfers/Mosci/GetInmates
        public JsonResult GetInmates()
        {
            return Json(_mosciList);
        }

        // POST: Transfers/Mosci/FindInmate
        [HttpPost]
        public JsonResult FindInmate(string searchOffenderId)
        {
            try
            {
                // Log the received parameters for debugging
                // Console.WriteLine($"FindInmate called with searchOffenderId: {searchOffenderId}");

                if (string.IsNullOrWhiteSpace(searchOffenderId))
                {
                    return Json(new {
                        success = false,
                        message = "Offender ID cannot be empty."
                    });
                }

                // Extract the prefix (first character) and the numeric part
                string prefix = "";
                string offenderId = searchOffenderId.Trim();

                // If the search ID starts with a letter (prefix), extract it
                if (searchOffenderId.Length > 0 && char.IsLetter(searchOffenderId[0]))
                {
                    prefix = searchOffenderId[0].ToString().ToUpper();
                    offenderId = searchOffenderId[1..];
                }

                // Console.WriteLine($"Extracted prefix: '{prefix}', offenderId: '{offenderId}'");

                // Find all inmates that match the search criteria
                var matchingInmates = new List<MosciViewModel>();

                if (!string.IsNullOrEmpty(prefix))
                {
                    // Search by both prefix and ID
                    matchingInmates = _mosciList.Where(i =>
                        i.InmateIdPrefix.Equals(prefix, StringComparison.OrdinalIgnoreCase) &&
                        i.OffenderId.Equals(offenderId, StringComparison.OrdinalIgnoreCase)).ToList();
                }
                else
                {
                    // Search by ID only (across all prefixes)
                    matchingInmates = _mosciList.Where(i =>
                        i.OffenderId.Equals(offenderId, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                // Console.WriteLine($"Found {matchingInmates.Count} matching inmates");

                // If no matches found, return an empty list with a message
                if (matchingInmates.Count == 0)
                {
                    return Json(new {
                        success = false,
                        message = "No inmates found matching the search criteria."
                    });
                }

                // Convert matching inmates to JavaScript-friendly format
                var inmates = matchingInmates.Select(inmate => new
                {
                    inmateIdPrefix = inmate.InmateIdPrefix,
                    offenderId = inmate.OffenderId,
                    // Add a combined ID field that includes the prefix
                    combinedOffenderId = inmate.InmateIdPrefix + inmate.OffenderId,
                    lastName = inmate.LastName,
                    firstName = inmate.FirstName,
                    schdInst = inmate.SchdInst,
                    instno = inmate.Instno,
                    oid = inmate.Oid,
                    recno = inmate.Recno,
                    descrl = inmate.Descrl,
                    sts = inmate.Sts,
                    stationame = inmate.Stationame,
                    romid = inmate.Romid,
                    schDate = inmate.SchDate.ToString("MM/dd/yyyy"),
                    sysDate = inmate.SysDate.ToString("MM/dd/yyyy")
                }).ToList();

                return Json(new {
                    success = true,
                    inmates = inmates,
                    count = inmates.Count,
                    message = $"Found {inmates.Count} inmate(s) matching the search criteria."
                });
            }
            catch (Exception ex)
            {
                return Json(new {
                    success = false,
                    message = "Error finding inmate: " + ex.Message
                });
            }
        }
    }
}
