// MOSCI Application JavaScript Functions

// Global namespace for MOSCI application
var MOSCI = MOSCI || {};

// Maximum number of rows allowed in the table
MOSCI.MAX_ROWS = 19;

// API endpoints
MOSCI.API = {
    FIND_INMATE: '/Transfers/Mosci/FindInmate'
};

// Clear the table except for the first row
MOSCI.clearTable = function() {
    console.log('Clearing table');

    // Keep the first row, remove all others
    var $firstRow = $('#inmateTable tbody tr:first');
    $('#inmateTable tbody tr:not(:first)').remove();

    // Clear the first row's input values
    $firstRow.find('input[type="text"]').val('');
    $firstRow.find('select').prop('selectedIndex', 0);
    $firstRow.find('input[type="checkbox"]').prop('checked', false);

    // Also clear the search input field
    $('#txtInmateNum').val('');

    console.log('Table cleared');
    return $firstRow;
};

// Add a new inmate row to the table
MOSCI.addNewInmate = function() {
    console.log('Adding new inmate row');

    // Check if we've reached the maximum number of rows
    var currentRowCount = $('#inmateTable tbody tr').length;
    if (currentRowCount >= MOSCI.MAX_ROWS) {
        alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
        return null;
    }

    // Clone the first row
    var $newRow = $('#inmate-row-template').clone();

    // Generate a unique ID for the new row
    var rowId = 'inmate-row-' + new Date().getTime();
    $newRow.attr('id', rowId);

    // Clear all input values
    $newRow.find('input[type="text"]').val('');
    $newRow.find('select').prop('selectedIndex', 0);
    $newRow.find('input[type="checkbox"]').prop('checked', false);

    // Append the new row to the table
    $('#inmateTable tbody').append($newRow);

    console.log('New inmate row added with ID: ' + rowId);
    return $newRow;
};

// Initialize the application
MOSCI.init = function() {
    // Attach event handlers
    MOSCI.attachEventHandlers();

    // Initialize numeric validation
    MOSCI.initNumericValidation();

    console.log('MOSCI initialized');
};

// Attach event handlers to buttons and form elements
MOSCI.attachEventHandlers = function() {
    console.log('Attaching event handlers');

    // Add New Inmate button
    //$('#btnAddNewInmate').on('click', MOSCI.addNewInmate);

    // Remove Inmate button
    //$('button[name="btnRemoveInmate"]').on('click', MOSCI.removeInmate);

    // Delete button
    $('button[name="btnDelete"]').on('click', MOSCI.deleteInmate);

    // Find Offender button - handled in Index.cshtml
    // $('#btnFindOffender').on('click', MOSCI.findOffender);

    // Cancel button
    $('#btnCancel').on('click', MOSCI.cancelForm);
};

// Add a new inmate row to the table
MOSCI.addNewInmate = function(e) {
    if (e) e.preventDefault();

    console.log('Adding new inmate row');

    // Check if we've reached the maximum number of rows
    var currentRowCount = $('#inmateTable tbody tr').length;
    if (currentRowCount >= MOSCI.MAX_ROWS) {
        alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
        return null;
    }

    // Create a new row based on the first row or create a new one if table is empty
    var $newRow;

    if ($('#inmateTable tbody tr').length > 0) {
        // Clone the first row
        $newRow = $('#inmateTable tbody tr:first').clone();
    } else {
        // Create a new row from scratch
        $newRow = $('<tr></tr>');
        $newRow.html(`
            <td>
                <select name="InmateIdPrefix" class="form-control input-sm">
                    <option value="A">A</option>
                    <option value="R">R</option>
                    <option value="W">W</option>
                </select>
            </td>
            <td><input type="text" name="OffenderId" class="form-control input-sm" value="" /></td>
            <td><input type="text" name="LastName" class="form-control input-sm" value="" /></td>
            <td><input type="text" name="FirstName" class="form-control input-sm" value="" /></td>
            <td>
                <select name="FromInstitution" class="form-control input-sm">
                    <option>MANCINI</option>
                </select>
            </td>
            <td><input type="text" name="SchdInst" class="form-control input-sm" value="" /></td>
            <td><input type="text" name="SchDate" class="form-control input-sm" value="" /></td>
            <td><input type="text" name="Descrl" class="form-control input-sm" value="" /></td>
            <td class="text-center"><input type="checkbox" name="Remove" /></td>
            <td class="text-center"><input type="checkbox" name="Delete" /></td>
        `);
    }

    // Generate a unique ID for the new row
    var rowId = 'inmate-row-' + new Date().getTime();
    $newRow.attr('id', rowId);

    // Clear all input values
    $newRow.find('input[type="text"]').val('');
    $newRow.find('select').prop('selectedIndex', 0);
    $newRow.find('input[type="checkbox"]').prop('checked', false);

    // Append the new row to the table
    $('#inmateTable tbody').append($newRow);

    console.log('New row added with ID: ' + rowId);

    // Scroll to the new row
    $('html, body').animate({
        scrollTop: $newRow.offset().top - 100
    }, 500);

    return $newRow;
};

// Remove selected inmates
MOSCI.removeInmate = function() {
    console.log('Removing selected inmates');

    var hasChecked = false;
    var checkedCount = 0;
    var totalRows = $('#inmateTable tbody tr').length;

    // Count how many rows are checked
    $('#inmateTable tbody tr').each(function() {
        var $row = $(this);
        if ($row.find('input[name="Remove"]').is(':checked')) {
            checkedCount++;
        }
    });

    // Check if removing would leave at least one row
    if (totalRows - checkedCount < 1) {
        alert('Cannot remove all rows. At least one row must remain in the table.');
        return;
    }

    // Remove the checked rows
    $('#inmateTable tbody tr').each(function() {
        var $row = $(this);
        if ($row.find('input[name="Remove"]').is(':checked')) {
            $row.remove();
            hasChecked = true;
        }
    });

    if (!hasChecked) {
        alert('Please select at least one inmate to remove.');
    }
};

// Delete selected inmates
MOSCI.deleteInmate = function() {
    console.log('Deleting selected inmates');

    var hasChecked = false;
    var checkedCount = 0;
    var totalRows = $('#inmateTable tbody tr').length;

    // Count how many rows are checked
    $('#inmateTable tbody tr').each(function() {
        var $row = $(this);
        if ($row.find('input[name="Delete"]').is(':checked')) {
            checkedCount++;
        }
    });

    // Check if deleting would leave at least one row
    if (totalRows - checkedCount < 1) {
        alert('Cannot delete all rows. At least one row must remain in the table.');
        return;
    }

    // Delete the checked rows
    $('#inmateTable tbody tr').each(function() {
        var $row = $(this);
        if ($row.find('input[name="Delete"]').is(':checked')) {
            $row.remove();
            hasChecked = true;
        }
    });

    if (!hasChecked) {
        alert('Please select at least one inmate to delete.');
    }
};

// Find an offender
MOSCI.findOffender = function() {
    console.log('Finding offender');

    var prefix = $('select[name="InmateIdPrefix"]').first().val();
    var offenderId = $('#txtInmateNum').val();

    if (!offenderId) {
        alert('Please enter an Offender ID.');
        return;
    }

    // Trim any whitespace
    offenderId = offenderId.trim();

    // Concatenate the prefix with the offender ID for searching
    var searchOffenderId = prefix + offenderId;
    console.log('Searching with combined ID:', searchOffenderId);

    // Show loading indicator
    $('#btnFindOffender').prop('disabled', true).html('<span class="glyphicon glyphicon-refresh glyphicon-spin"></span> Searching...');

    // Clear the table except for the first empty row
    var $firstRow = MOSCI.clearTable();

    // Make AJAX call to find inmate
    $.ajax({
        url: MOSCI.API.FIND_INMATE,
        type: 'POST',
        data: { searchOffenderId: searchOffenderId },
        success: function(result) {
            // Reset button
            $('#btnFindOffender').prop('disabled', false).html('Find Inmate');

            // Log the entire result for debugging
            console.log('Server response:', result);

            if (result.success) {
                var inmates = result.inmates;
                var count = result.count;

                // Check if the first row is empty (default row)
                var isFirstRowEmpty = $firstRow.find('input[name="OffenderId"]').val() === '';

                // Loop through all matching inmates
                for (var i = 0; i < inmates.length; i++) {
                    var inmate = inmates[i];
                    var $currentRow;

                    // For the first inmate, use the first row if it's empty
                    if (i === 0 && isFirstRowEmpty) {
                        console.log('Using the first empty row for inmate #1');
                        $currentRow = $firstRow;
                    } else {
                        // Check if we've reached the maximum number of rows
                        if ($('#inmateTable tbody tr').length >= MOSCI.MAX_ROWS) {
                            alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more inmates.');
                            break;
                        }

                        // Add a new row with the inmate data
                        $currentRow = MOSCI.addNewInmate();
                        if (!$currentRow) {
                            console.error('Failed to add new row for inmate #' + (i + 1));
                            continue;
                        }
                    }

                    // Find the select element for InmateIdPrefix
                    var $select = $currentRow.find('select[name="InmateIdPrefix"]');

                    // Clear any existing selected options
                    $select.find('option').prop('selected', false);

                    // Select the option with the matching value
                    $select.find('option[value="' + inmate.inmateIdPrefix + '"]').prop('selected', true);

                    // Set other field values
                    $currentRow.find('input[name="OffenderId"]').val(inmate.combinedOffenderId);
                    $currentRow.find('input[name="LastName"]').val(inmate.lastName);
                    $currentRow.find('input[name="FirstName"]').val(inmate.firstName);
                    $currentRow.find('input[name="SchdInst"]').val(inmate.schdInst);
                    $currentRow.find('input[name="SchDate"]').val(inmate.schDate);
                    $currentRow.find('input[name="Descrl"]').val(inmate.descrl);

                    console.log('Inmate #' + (i + 1) + ' added to ' + (i === 0 && isFirstRowEmpty ? 'first row' : 'a new row'));
                }

                // Show success message
                alert(result.message);
            } else {
                // Show error message
                alert('Error: ' + result.message);
            }
        },
        error: function(xhr, status, error) {
            // Reset button
            $('#btnFindOffender').prop('disabled', false).html('Search');

            // Show error message
            console.error('AJAX Error:', status, error);
            alert('Error finding inmate. See console for details.');
        }
    });
};

// Cancel the form
MOSCI.cancelForm = function() {
    console.log('Canceling form');

    if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
        window.location.href = '/Home/Index';
    }
};

// Initialize numeric validation for offender ID
MOSCI.initNumericValidation = function() {
    $('.onlyNumeric').on('keypress', function(e) {
        // Allow only numbers (0-9)
        if (e.which < 48 || e.which > 57) {
            e.preventDefault();
        }
    });
};

// Initialize when the document is ready
$(document).ready(function() {
    console.log('Document ready, initializing MOSCI application');

    // Add a debug message to the page
    if ($('#inmateTable').length > 0) {
        console.log('MOSCI table found, initializing MOSCI functionality');

        // Initialize MOSCI functionality
        MOSCI.init();

        // Add a debug message to verify initialization
        setTimeout(function() {
            console.log('MOSCI initialization complete');

            // Display row count
            var rowCount = $('#inmateTable tbody tr').length;
            console.log('Current row count: ' + rowCount);
        }, 500);
    } else {
        console.log('MOSCI table not found, skipping MOSCI initialization');
    }
});
