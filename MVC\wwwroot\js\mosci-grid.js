$(document).ready(function () {
    // Constants for grid limits
    const MIN_ROWS = 1;
    const MAX_ROWS = 19;

    // Function to get current row count
    function getRowCount() {
        return $('#inmateTable tbody tr').length;
    }

    // Function to validate row operations
    function validateRowOperation(operation, currentRows) {
        if (operation === 'add' && currentRows >= MAX_ROWS) {
            alert('Maximum limit of ' + MAX_ROWS + ' rows reached.');
            return false;
        }
        if (operation === 'remove' && currentRows <= MIN_ROWS) {
            alert('Minimum of ' + MIN_ROWS + ' row required.');
            return false;
        }
        return true;
    }

    // Add New Inmate button click handler
    $('#btnAddNewInmate').click(function () {
        const currentRows = getRowCount();
        if (!validateRowOperation('add', currentRows)) return;

        var newRow = $('<tr>');
        // Clone the structure of the first row
        var firstRow = $('#inmateTable tbody tr:first').clone();
        // Clear the input values
        firstRow.find('input[type="text"]').val('');
        firstRow.find('input[type="checkbox"]').prop('checked', false);
        firstRow.find('select').prop('selectedIndex', 0);
        newRow.html(firstRow.html());
        $('#inmateTable tbody').append(newRow);
    });

    // Remove Inmate button click handler
    $('button[name="btnRemoveInmate"]').click(function () {
        const currentRows = getRowCount();
        if (!validateRowOperation('remove', currentRows)) return;

        $('#inmateTable tbody tr').each(function () {
            if ($(this).find('input[name="Remove"]').prop('checked')) {
                $(this).remove();
            }
        });
    });

    // Delete button click handler
    $('button[name="btnDelete"]').click(function () {
        const currentRows = getRowCount();
        if (!validateRowOperation('remove', currentRows)) return;

        $('#inmateTable tbody tr').each(function () {
            if ($(this).find('input[name="Delete"]').prop('checked')) {
                $(this).remove();
            }
        });
    });

    // Save button click handler
    $('#btnSave').click(function () {
        var inmates = [];
        $('#inmateTable tbody tr').each(function () {
            var inmate = {
                InmateIdPrefix: $(this).find('select[name="InmateIdPrefix"]').val(),
                OffenderId: $(this).find('input[name="OffenderId"]').val(),
                LastName: $(this).find('input[name="LastName"]').val(),
                FirstName: $(this).find('input[name="FirstName"]').val(),
                SchdInst: $(this).find('input[name="SchdInst"]').val(),
                SchDate: $(this).find('input[name="SchDate"]').val(),
                Descrl: $(this).find('input[name="Descrl"]').val()
            };
            inmates.push(inmate);
        });

        $('#modelJson').val(JSON.stringify(inmates));
        $('form').submit();
    });
});