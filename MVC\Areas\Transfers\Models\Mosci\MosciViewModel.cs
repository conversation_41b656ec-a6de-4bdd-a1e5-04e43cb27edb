using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Odrdc.Dots.Areas.Transfers.Models.Mosci
{
    public class MosciViewModel
    {
        public DateTime SchDate { get; set; }
        public string SchdInst { get; set; } = "";
        public string Instno { get; set; } = "";
        public string Oid { get; set; } = "";
        public int Recno { get; set; }
        public string Descrl { get; set; } = "";
        public string Sts { get; set; } = "";
        public string Stationame { get; set; } = "";
        public DateTime SysDate { get; set; }
        public string Romid { get; set; } = "";
        public string LastName { get; set; } = "";
        public string FirstName { get; set; } = "";
        public string InmateIdPrefix { get; set; } = "";
        public string OffenderId { get; set; } = "";
        public List<SelectListItem> Prefix { get; set; } = new List<SelectListItem>();

        // Combined property for display purposes
        public string CombinedOffenderId
        {
            get => string.IsNullOrEmpty(InmateIdPrefix) ? OffenderId : InmateIdPrefix + OffenderId;
            set
            {
                // When setting combined ID, extract prefix and offender ID
                if (!string.IsNullOrEmpty(value) && value.Length > 0)
                {
                    if (char.IsLetter(value[0]))
                    {
                        InmateIdPrefix = value[0].ToString().ToUpper();
                        OffenderId = value.Length > 1 ? value[1..] : "";
                    }
                    else
                    {
                        InmateIdPrefix = ""; // Don't set default prefix
                        OffenderId = value;
                    }
                }
                else
                {
                    InmateIdPrefix = "";
                    OffenderId = "";
                }
            }
        }

        // Properties for row operations
        public bool IsSelected { get; set; }
        public bool IsMarkedForRemoval { get; set; }
        public bool IsMarkedForDeletion { get; set; }
    }

    public class MosciPageViewModel
    {
        public MosciPageViewModel()
        {
            Inmates = new List<MosciViewModel>();
            SearchPrefix = "A";
            SearchOffenderId = "";
            PrefixOptions = new List<SelectListItem>
            {
                new SelectListItem { Value = "A", Text = "A" },
                new SelectListItem { Value = "R", Text = "R" },
                new SelectListItem { Value = "W", Text = "W" }
            };
        }

        // Search parameters
        [Display(Name = "Prefix")]
        public string SearchPrefix { get; set; } = "";

        [Display(Name = "Offender ID")]
        [StringLength(6, ErrorMessage = "Offender ID cannot exceed 6 characters")]
        public string SearchOffenderId { get; set; } = "";

        // List of inmates for the grid
        public List<MosciViewModel> Inmates { get; set; }

        // Dropdown options
        public List<SelectListItem> PrefixOptions { get; set; }

        // UI state
        public string Message { get; set; } = "";
        public string ErrorMessage { get; set; } = "";
        public bool HasSearchResults { get; set; }

        // Maximum number of rows allowed
        public const int MaxRows = 19;
    }
}